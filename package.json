{"name": "vtb-front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@cerebras/cerebras_cloud_sdk": "^1.46.0", "@prisma/client": "^6.15.0", "@radix-ui/react-slot": "^1.2.3", "@types/node": "^24.3.0", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "bull": "^4.16.5", "bullmq": "^5.58.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "next": "15.5.2", "prisma": "^6.15.0", "react": "19.1.0", "react-dom": "19.1.0", "redis": "^5.8.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "typescript": "^5.9.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "tsx": "^4.20.5", "tw-animate-css": "^1.3.7"}}