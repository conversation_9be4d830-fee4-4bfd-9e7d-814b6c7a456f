import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { name, email, resumeUrl } = await request.json()

    if (!name || !email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      )
    }

    // Check if candidate with this email already exists
    const existingCandidate = await prisma.candidate.findUnique({
      where: { email }
    })

    if (existingCandidate) {
      return NextResponse.json(
        { error: 'Candidate with this email already exists' },
        { status: 409 }
      )
    }

    const candidate = await prisma.candidate.create({
      data: {
        name,
        email,
        resumeUrl: resumeUrl || null
      }
    })

    return NextResponse.json(candidate, { status: 201 })
  } catch (error) {
    console.error('Error creating candidate:', error)
    return NextResponse.json(
      { error: 'Failed to create candidate' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const candidates = await prisma.candidate.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        interviews: {
          include: {
            job: true
          }
        }
      }
    })

    return NextResponse.json(candidates)
  } catch (error) {
    console.error('Error getting candidates:', error)
    return NextResponse.json(
      { error: 'Failed to get candidates' },
      { status: 500 }
    )
  }
}
