'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'

interface Interview {
  id: string
  status: string
  startedAt?: string
  endedAt?: string
  evaluationScore?: number
  candidate: {
    name: string
    email: string
  }
  job: {
    title: string
  }
}

interface JobDescription {
  id: string
  title: string
  description: string
  requirements: string
  createdAt: string
}

export default function RecruiterPage() {
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [jobs, setJobs] = useState<JobDescription[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'interviews' | 'jobs'>('interviews')
  const [jobForm, setJobForm] = useState({
    title: '',
    description: '',
    requirements: ''
  })
  const [showJobForm, setShowJobForm] = useState(false)

  useEffect(() => {
    fetchInterviews()
    fetchJobs()
  }, [])

  const fetchInterviews = async () => {
    try {
      const response = await fetch('/api/interviews')
      if (response.ok) {
        const data = await response.json()
        setInterviews(data)
      }
    } catch (error) {
      console.error('Error fetching interviews:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchJobs = async () => {
    try {
      const response = await fetch('/api/jobs')
      if (response.ok) {
        const data = await response.json()
        setJobs(data)
      }
    } catch (error) {
      console.error('Error fetching jobs:', error)
    }
  }

  const createJob = async () => {
    try {
      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(jobForm)
      })

      if (response.ok) {
        const newJob = await response.json()
        setJobs(prev => [newJob, ...prev])
        setJobForm({ title: '', description: '', requirements: '' })
        setShowJobForm(false)
      }
    } catch (error) {
      console.error('Error creating job:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'default'
      case 'IN_PROGRESS':
        return 'secondary'
      case 'SCHEDULED':
        return 'outline'
      case 'CANCELLED':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const getScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500'
    if (score >= 8) return 'text-green-600'
    if (score >= 6) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Recruiter Dashboard</h1>
          <p className="text-gray-600">Manage interviews and job postings</p>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <div className="flex space-x-4">
            <Button
              variant={activeTab === 'interviews' ? 'default' : 'outline'}
              onClick={() => setActiveTab('interviews')}
            >
              Interviews ({interviews.length})
            </Button>
            <Button
              variant={activeTab === 'jobs' ? 'default' : 'outline'}
              onClick={() => setActiveTab('jobs')}
            >
              Job Postings ({jobs.length})
            </Button>
          </div>
        </div>

        {/* Interviews Tab */}
        {activeTab === 'interviews' && (
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-4">Recent Interviews</h2>
              {interviews.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <p className="text-gray-500 mb-4">No interviews conducted yet.</p>
                    <p className="text-sm text-gray-400">Interviews will appear here once candidates start applying.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                  {interviews.map((interview) => (
                    <Card key={interview.id}>
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg">{interview.candidate.name}</CardTitle>
                            <p className="text-sm text-gray-600">{interview.candidate.email}</p>
                          </div>
                          <Badge variant={getStatusColor(interview.status)}>
                            {interview.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <p className="text-sm">
                            <span className="font-medium">Position:</span> {interview.job.title}
                          </p>
                          {interview.startedAt && (
                            <p className="text-sm">
                              <span className="font-medium">Started:</span>{' '}
                              {new Date(interview.startedAt).toLocaleString()}
                            </p>
                          )}
                          {interview.endedAt && (
                            <p className="text-sm">
                              <span className="font-medium">Completed:</span>{' '}
                              {new Date(interview.endedAt).toLocaleString()}
                            </p>
                          )}
                          {interview.evaluationScore && (
                            <p className="text-sm">
                              <span className="font-medium">Score:</span>{' '}
                              <span className={getScoreColor(interview.evaluationScore)}>
                                {interview.evaluationScore.toFixed(1)}/10
                              </span>
                            </p>
                          )}
                        </div>
                        <div className="mt-4">
                          <Link href={`/interview/${interview.id}`}>
                            <Button variant="outline" size="sm" className="w-full">
                              View Details
                            </Button>
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Jobs Tab */}
        {activeTab === 'jobs' && (
          <div>
            <div className="mb-6 flex justify-between items-center">
              <h2 className="text-xl font-semibold">Job Postings</h2>
              <Button onClick={() => setShowJobForm(!showJobForm)}>
                {showJobForm ? 'Cancel' : 'Create New Job'}
              </Button>
            </div>

            {/* Job Creation Form */}
            {showJobForm && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Create New Job Posting</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <input
                      type="text"
                      placeholder="Job Title"
                      value={jobForm.title}
                      onChange={(e) => setJobForm(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <textarea
                      placeholder="Job Description"
                      value={jobForm.description}
                      onChange={(e) => setJobForm(prev => ({ ...prev, description: e.target.value }))}
                      rows={4}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <textarea
                      placeholder="Requirements"
                      value={jobForm.requirements}
                      onChange={(e) => setJobForm(prev => ({ ...prev, requirements: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <Button 
                      onClick={createJob}
                      disabled={!jobForm.title || !jobForm.description || !jobForm.requirements}
                    >
                      Create Job Posting
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Jobs List */}
            {jobs.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <p className="text-gray-500 mb-4">No job postings yet.</p>
                  <Button onClick={() => setShowJobForm(true)}>
                    Create Your First Job Posting
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {jobs.map((job) => (
                  <Card key={job.id}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{job.title}</CardTitle>
                        <Badge variant="outline">
                          {new Date(job.createdAt).toLocaleDateString()}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium mb-1">Description:</h4>
                          <p className="text-sm text-gray-600 line-clamp-3">{job.description}</p>
                        </div>
                        <div>
                          <h4 className="font-medium mb-1">Requirements:</h4>
                          <p className="text-sm text-gray-600 line-clamp-2">{job.requirements}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link href="/">
            <Button variant="outline">Back to Home</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
