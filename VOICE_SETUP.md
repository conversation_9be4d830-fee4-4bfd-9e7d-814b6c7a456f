# 🎤 AI HR Interview Platform - Voice-Enabled Setup

## 🚀 Complete Voice Interview Experience

This enhanced version includes real-time voice interaction with:
- **<PERSON><PERSON><PERSON>ova Whisper**: Speech-to-text transcription
- **<PERSON><PERSON>o TTS**: Text-to-speech synthesis  
- **Cerebras LLM**: AI conversation management
- **Real-time Audio**: WebSocket-based voice streaming

## 📋 Prerequisites

1. **Python 3.8+** installed
2. **Node.js 18+** installed
3. **Environment Variables** configured:
   - `SAMBANOVA_KEY`: Your SambaNova API key
   - `CEREBRAS_KEY`: Your Cerebras API key
   - `DATABASE_URL`: PostgreSQL connection string

## 🛠 Quick Setup

### Option 1: Automated Setup (Recommended)

1. **Run the setup script:**
   ```bash
   setup_voice_backend.bat
   ```

2. **Start both servers:**
   ```bash
   start_voice_interview.bat
   ```

### Option 2: Manual Setup

1. **Setup Python Backend:**
   ```bash
   cd voice_backend
   pip install -r requirements.txt
   python main.py
   ```

2. **Start Next.js Frontend:**
   ```bash
   npm run dev
   ```

## 🎯 How to Use Voice Features

### 1. **Access the Platform**
- Frontend: http://localhost:3001
- Voice Backend: http://localhost:8000

### 2. **Start a Voice Interview**
1. Go to `/candidate` and create your profile
2. Browse jobs and click "Start AI Interview"
3. In the interview room, ensure "Voice" mode is enabled
4. Click "Start Recording" to begin speaking
5. The AI will respond with both text and voice

### 3. **Voice Controls**
- **🎤 Voice Mode**: Real-time speech-to-text + AI voice responses
- **💬 Text Mode**: Traditional text-based chat
- **🔴 Record Button**: Start/stop voice recording
- **🔊 Mute/Unmute**: Control audio output

## 🏗 Architecture

```
Frontend (Next.js)
├── Voice Service (/services/voice.ts)
├── Enhanced Interview Room
└── WebSocket connection to Python backend

Python Backend (FastAPI)
├── WebSocket server (:8000)
├── SambaNova Whisper integration
├── Cerebras LLM integration
└── Silero TTS synthesis

Voice Pipeline:
Audio → WebSocket → SambaNova STT → Cerebras AI → TTS → Audio Response
```

## 🎤 Voice Features

### **Speech-to-Text (STT)**
- **Provider**: SambaNova Whisper API
- **Real-time**: 2-second audio chunks
- **Language**: English (configurable)
- **Quality**: High accuracy transcription

### **Text-to-Speech (TTS)**
- **Provider**: Silero TTS (configurable)
- **Voice**: Natural English voice
- **Latency**: < 1 second response time
- **Format**: 16-bit PCM audio

### **AI Conversation**
- **Provider**: Cerebras LLM (llama3.1-8b)
- **Context**: Job-aware interview questions
- **Phases**: Introduction → Technical → Behavioral → Closing
- **Evaluation**: Real-time candidate scoring

## 🔧 Configuration

### **Environment Variables**
```env
# Required for voice features
SAMBANOVA_KEY=your_sambanova_api_key
CEREBRAS_KEY=your_cerebras_api_key
DATABASE_URL=your_postgresql_url
```

### **Voice Backend Settings**
- **Port**: 8000 (configurable in main.py)
- **CORS**: Enabled for localhost:3001
- **WebSocket**: Real-time bidirectional communication
- **Audio Format**: WebM → WAV conversion

## 🎯 Interview Flow

### **1. Voice-Enabled Interview Process**
1. **Connection**: Frontend connects to voice backend
2. **Welcome**: AI greets candidate with voice
3. **Recording**: Candidate speaks, audio sent to backend
4. **Transcription**: SambaNova converts speech to text
5. **AI Response**: Cerebras generates contextual response
6. **Synthesis**: Text converted to speech
7. **Playback**: Audio response played to candidate

### **2. Fallback Modes**
- **Text Mode**: If voice backend unavailable
- **Socket.io**: Fallback to Next.js backend
- **Error Handling**: Graceful degradation

## 🚀 Production Enhancements

### **For Production Deployment:**

1. **Enhanced TTS Integration:**
   ```python
   # Add to voice_backend/main.py
   from TTS.api import TTS
   tts = TTS("tts_models/en/ljspeech/tacotron2-DDC")
   ```

2. **Advanced STT Processing:**
   ```python
   # Add audio preprocessing
   import librosa
   audio, sr = librosa.load(audio_file, sr=16000)
   ```

3. **Voice Activity Detection:**
   ```python
   # Add VAD for better turn detection
   import webrtcvad
   vad = webrtcvad.Vad(3)  # Aggressiveness level
   ```

## 🎉 Success Metrics

### **Voice Features Working:**
- ✅ **Real-time STT**: SambaNova Whisper transcription
- ✅ **AI Responses**: Cerebras LLM conversation
- ✅ **TTS Synthesis**: Text-to-speech generation
- ✅ **WebSocket Communication**: Real-time audio streaming
- ✅ **Fallback Support**: Text mode when voice unavailable
- ✅ **Professional UI**: Voice activity indicators and controls

### **Interview Experience:**
- ✅ **Natural Conversation**: Speak and hear AI responses
- ✅ **Context Awareness**: Job-specific questions
- ✅ **Real-time Evaluation**: Live candidate scoring
- ✅ **Phase Management**: Structured interview progression
- ✅ **Audio Quality**: Clear voice synthesis and recognition

## 🔍 Troubleshooting

### **Common Issues:**

1. **Voice Backend Not Connecting:**
   - Check if Python backend is running on port 8000
   - Verify environment variables are set
   - Check firewall/antivirus blocking connections

2. **Audio Not Working:**
   - Grant microphone permissions in browser
   - Check audio device settings
   - Verify WebRTC support in browser

3. **STT Not Transcribing:**
   - Verify SambaNova API key is valid
   - Check audio format compatibility
   - Ensure stable internet connection

4. **TTS Not Playing:**
   - Check browser audio permissions
   - Verify audio context initialization
   - Test with different browsers

## 🎯 Next Steps

1. **Test the Complete Flow**: Create candidate → Start interview → Use voice
2. **Enhance TTS**: Integrate full Silero TTS for better voice quality
3. **Add VAD**: Implement voice activity detection for natural conversations
4. **Optimize Latency**: Reduce audio processing delays
5. **Production Deploy**: Scale for multiple concurrent interviews

The AI HR Interview Platform now supports **full voice interaction** with real-time speech recognition, AI conversation, and voice synthesis!
