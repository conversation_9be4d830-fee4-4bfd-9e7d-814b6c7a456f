import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'POST':
        return await createJob(req, res)
      case 'GET':
        return await getJobs(req, res)
      default:
        res.setHeader('Allow', ['POST', 'GET'])
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('API Error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

async function createJob(req: NextApiRequest, res: NextApiResponse) {
  const { title, description, requirements } = req.body

  if (!title || !description || !requirements) {
    return res.status(400).json({ error: 'Title, description, and requirements are required' })
  }

  try {
    const job = await prisma.jobDescription.create({
      data: {
        title,
        description,
        requirements
      }
    })

    return res.status(201).json(job)
  } catch (error) {
    console.error('Error creating job:', error)
    return res.status(500).json({ error: 'Failed to create job' })
  }
}

async function getJobs(req: NextApiRequest, res: NextApiResponse) {
  try {
    const jobs = await prisma.jobDescription.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        interviews: {
          include: {
            candidate: true
          }
        }
      }
    })

    return res.status(200).json(jobs)
  } catch (error) {
    console.error('Error getting jobs:', error)
    return res.status(500).json({ error: 'Failed to get jobs' })
  }
}
