import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'POST':
        return await createCandidate(req, res)
      case 'GET':
        return await getCandidates(req, res)
      default:
        res.setHeader('Allow', ['POST', 'GET'])
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('API Error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

async function createCandidate(req: NextApiRequest, res: NextApiResponse) {
  const { name, email, resumeUrl } = req.body

  if (!name || !email) {
    return res.status(400).json({ error: 'Name and email are required' })
  }

  try {
    // Check if candidate with this email already exists
    const existingCandidate = await prisma.candidate.findUnique({
      where: { email }
    })

    if (existingCandidate) {
      return res.status(409).json({ error: 'Candidate with this email already exists' })
    }

    const candidate = await prisma.candidate.create({
      data: {
        name,
        email,
        resumeUrl: resumeUrl || null
      }
    })

    return res.status(201).json(candidate)
  } catch (error) {
    console.error('Error creating candidate:', error)
    return res.status(500).json({ error: 'Failed to create candidate' })
  }
}

async function getCandidates(req: NextApiRequest, res: NextApiResponse) {
  try {
    const candidates = await prisma.candidate.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        interviews: {
          include: {
            job: true
          }
        }
      }
    })

    return res.status(200).json(candidates)
  } catch (error) {
    console.error('Error getting candidates:', error)
    return res.status(500).json({ error: 'Failed to get candidates' })
  }
}
