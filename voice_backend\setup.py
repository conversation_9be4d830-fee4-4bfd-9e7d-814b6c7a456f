#!/usr/bin/env python3
"""
Setup script for AI Interview Voice Backend
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {command}")
        print(f"Error: {e.stderr}")
        return False

def main():
    print("🚀 Setting up AI Interview Voice Backend...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version}")
    
    # Create virtual environment
    print("\n📦 Creating virtual environment...")
    if not run_command("python -m venv venv"):
        print("❌ Failed to create virtual environment")
        sys.exit(1)
    
    # Activate virtual environment and install dependencies
    print("\n📥 Installing dependencies...")
    
    # Determine activation script based on OS
    if os.name == 'nt':  # Windows
        activate_script = "venv\\Scripts\\activate"
        pip_command = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        activate_script = "source venv/bin/activate"
        pip_command = "venv/bin/pip"
    
    # Install dependencies
    commands = [
        f"{pip_command} install --upgrade pip",
        f"{pip_command} install -r requirements.txt",
        f"{pip_command} install torch torchaudio --index-url https://download.pytorch.org/whl/cpu"
    ]
    
    for command in commands:
        if not run_command(command):
            print(f"❌ Failed to run: {command}")
            sys.exit(1)
    
    print("\n✅ Setup completed successfully!")
    print("\n🎯 To start the voice backend:")
    print("1. Copy your .env file to this directory")
    print("2. Run: python main.py")
    print("3. The server will start on http://localhost:8000")

if __name__ == "__main__":
    main()
