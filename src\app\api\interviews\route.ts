import { NextRequest, NextResponse } from 'next/server'
import { interviewService } from '@/services/interview'

export async function POST(request: NextRequest) {
  try {
    const { candidateId, jobId } = await request.json()

    if (!candidateId || !jobId) {
      return NextResponse.json(
        { error: 'candidateId and jobId are required' },
        { status: 400 }
      )
    }

    const interview = await interviewService.createInterview({
      candidateId,
      jobId
    })

    return NextResponse.json(interview, { status: 201 })
  } catch (error) {
    console.error('Error creating interview:', error)
    return NextResponse.json(
      { error: 'Failed to create interview' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const candidateId = searchParams.get('candidateId')

    let interviews
    if (candidateId) {
      interviews = await interviewService.getInterviewsByCandidate(candidateId)
    } else {
      interviews = await interviewService.getAllInterviews()
    }

    return NextResponse.json(interviews)
  } catch (error) {
    console.error('Error getting interviews:', error)
    return NextResponse.json(
      { error: 'Failed to get interviews' },
      { status: 500 }
    )
  }
}
