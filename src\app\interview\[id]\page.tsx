'use client'

import { useParams, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import <PERSON><PERSON><PERSON> from '@/components/InterviewRoom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

interface Interview {
  id: string
  candidateId: string
  jobId: string
  status: string
  candidate: {
    id: string
    name: string
    email: string
  }
  job: {
    id: string
    title: string
    description: string
  }
}

export default function InterviewPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const interviewId = params.id as string
  const candidateId = searchParams.get('candidateId') || ''
  const jobId = searchParams.get('jobId') || ''

  const [interview, setInterview] = useState<Interview | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (interviewId) {
      fetchInterview()
    }
  }, [interviewId])

  const fetchInterview = async () => {
    try {
      const response = await fetch(`/api/interviews/${interviewId}`)
      if (response.ok) {
        const data = await response.json()
        setInterview(data)
      } else {
        setError('Interview not found')
      }
    } catch (err) {
      setError('Failed to load interview')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading interview...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link href="/">
              <Button>Go Home</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!interview) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle>Interview Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              The interview you're looking for doesn't exist or has been removed.
            </p>
            <Link href="/">
              <Button>Go Home</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <InterviewRoom
      interviewId={interview.id}
      candidateId={interview.candidateId}
      jobId={interview.jobId}
    />
  )
}
