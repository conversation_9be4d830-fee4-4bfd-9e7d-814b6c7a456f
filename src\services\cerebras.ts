import { Cerebras } from '@cerebras/cerebras_cloud_sdk'

const cerebras = new Cerebras({
  apiKey: process.env.CEREBRAS_KEY,
})

export interface InterviewContext {
  jobDescription: string
  candidateResume?: string
  conversationHistory: Array<{
    role: 'assistant' | 'user'
    content: string
    timestamp: number
  }>
  currentPhase: 'introduction' | 'technical' | 'behavioral' | 'closing'
  evaluationCriteria: string[]
}

export interface AIResponse {
  message: string
  nextPhase?: InterviewContext['currentPhase']
  evaluation?: {
    criteria: string
    score: number
    feedback: string
  }
  shouldEnd?: boolean
}

export class CerebrasInterviewService {
  private buildSystemPrompt(context: InterviewContext): string {
    return `You are an AI HR interviewer conducting a professional job interview. 

Job Description: ${context.jobDescription}
${context.candidateResume ? `Candidate Resume: ${context.candidateResume}` : ''}

Current Interview Phase: ${context.currentPhase}

Guidelines:
1. Ask relevant questions based on the job requirements
2. Be professional, friendly, and encouraging
3. Listen carefully to responses and ask follow-up questions
4. Evaluate responses on: technical skills, communication, problem-solving, cultural fit
5. Keep responses concise (2-3 sentences max)
6. Progress through phases: introduction → technical → behavioral → closing

Evaluation Criteria: ${context.evaluationCriteria.join(', ')}

Conversation History:
${context.conversationHistory.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Respond as the interviewer with your next question or comment. If you need to evaluate the candidate's last response, include your assessment.`
  }

  async generateResponse(
    candidateMessage: string,
    context: InterviewContext
  ): Promise<AIResponse> {
    try {
      // Add candidate message to history
      context.conversationHistory.push({
        role: 'user',
        content: candidateMessage,
        timestamp: Date.now()
      })

      const systemPrompt = this.buildSystemPrompt(context)

      const completion = await cerebras.chat.completions.create({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: candidateMessage }
        ],
        model: 'llama3.1-8b',
        temperature: 0.7,
        max_tokens: 200,
      })

      const aiMessage = completion.choices[0]?.message?.content || 'I apologize, but I need a moment to process your response.'

      // Add AI response to history
      context.conversationHistory.push({
        role: 'assistant',
        content: aiMessage,
        timestamp: Date.now()
      })

      // Determine next phase based on conversation length and current phase
      let nextPhase = context.currentPhase
      const conversationLength = context.conversationHistory.length

      if (context.currentPhase === 'introduction' && conversationLength > 4) {
        nextPhase = 'technical'
      } else if (context.currentPhase === 'technical' && conversationLength > 10) {
        nextPhase = 'behavioral'
      } else if (context.currentPhase === 'behavioral' && conversationLength > 16) {
        nextPhase = 'closing'
      }

      // Generate evaluation for the candidate's response
      const evaluation = await this.evaluateResponse(candidateMessage, context)

      return {
        message: aiMessage,
        nextPhase: nextPhase !== context.currentPhase ? nextPhase : undefined,
        evaluation,
        shouldEnd: nextPhase === 'closing' && conversationLength > 18
      }
    } catch (error) {
      console.error('Error generating AI response:', error)
      return {
        message: 'I apologize, but I\'m experiencing technical difficulties. Could you please repeat your response?'
      }
    }
  }

  private async evaluateResponse(
    candidateMessage: string,
    context: InterviewContext
  ): Promise<AIResponse['evaluation']> {
    try {
      const evaluationPrompt = `Evaluate this candidate response on a scale of 1-10:

Response: "${candidateMessage}"
Job Requirements: ${context.jobDescription}
Interview Phase: ${context.currentPhase}

Provide a score (1-10) and brief feedback focusing on:
- Relevance to the question
- Technical accuracy (if applicable)
- Communication clarity
- Depth of response

Format: Score: X/10 | Feedback: [brief feedback]`

      const completion = await cerebras.chat.completions.create({
        messages: [
          { role: 'system', content: 'You are an expert HR evaluator. Provide concise, constructive feedback.' },
          { role: 'user', content: evaluationPrompt }
        ],
        model: 'llama3.1-8b',
        temperature: 0.3,
        max_tokens: 100,
      })

      const evaluationText = completion.choices[0]?.message?.content || ''
      const scoreMatch = evaluationText.match(/Score:\s*(\d+)/i)
      const feedbackMatch = evaluationText.match(/Feedback:\s*(.+)/i)

      return {
        criteria: `${context.currentPhase}_response`,
        score: scoreMatch ? parseInt(scoreMatch[1]) : 5,
        feedback: feedbackMatch ? feedbackMatch[1].trim() : 'Response noted.'
      }
    } catch (error) {
      console.error('Error evaluating response:', error)
      return {
        criteria: `${context.currentPhase}_response`,
        score: 5,
        feedback: 'Unable to evaluate response at this time.'
      }
    }
  }

  generateInitialQuestion(jobDescription: string): string {
    return `Hello! Welcome to your interview. I'm excited to learn more about you. Let's start with a simple question: Can you tell me about yourself and what interests you about this ${jobDescription.split('\n')[0] || 'position'}?`
  }
}

export const cerebrasService = new CerebrasInterviewService()
