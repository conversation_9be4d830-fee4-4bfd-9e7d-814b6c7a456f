import { prisma } from '../src/lib/prisma'

async function checkInterview() {
  try {
    const interviewId = 'cmezo6kbl0002vis07850tr0e'
    
    console.log(`🔍 Checking interview: ${interviewId}`)
    
    const interview = await prisma.interview.findUnique({
      where: { id: interviewId },
      include: {
        candidate: true,
        job: true,
        messages: true,
        evaluations: true
      }
    })
    
    if (interview) {
      console.log('✅ Interview found:')
      console.log(`   - ID: ${interview.id}`)
      console.log(`   - Candidate: ${interview.candidate.name}`)
      console.log(`   - Job: ${interview.job.title}`)
      console.log(`   - Status: ${interview.status}`)
      console.log(`   - Created: ${interview.createdAt}`)
    } else {
      console.log('❌ Interview not found in database')
      
      // Let's check what interviews do exist
      console.log('\n📋 Available interviews:')
      const allInterviews = await prisma.interview.findMany({
        include: {
          candidate: true,
          job: true
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      })
      
      if (allInterviews.length === 0) {
        console.log('   No interviews found in database')
      } else {
        allInterviews.forEach((int, index) => {
          console.log(`   ${index + 1}. ID: ${int.id}`)
          console.log(`      Candidate: ${int.candidate.name}`)
          console.log(`      Job: ${int.job.title}`)
          console.log(`      Status: ${int.status}`)
          console.log(`      URL: http://localhost:3001/interview/${int.id}`)
          console.log('')
        })
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking interview:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkInterview()
