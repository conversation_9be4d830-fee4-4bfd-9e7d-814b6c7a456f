<!DOCTYPE html>
<html>
<head>
    <title>Test API</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testDebugAPI()">Test Debug API</button>
    <button onclick="testInterviewAPI()">Test Interview API</button>
    <button onclick="testJobsAPI()">Test Jobs API</button>
    
    <div id="results"></div>

    <script>
        async function testDebugAPI() {
            try {
                const response = await fetch('http://localhost:3001/api/debug');
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Debug API:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Debug API Error:</h3><pre>' + error.message + '</pre>';
            }
        }

        async function testInterviewAPI() {
            try {
                const interviewId = 'cmezo6kbl0002vis07850tr0e';
                const response = await fetch(`http://localhost:3001/api/interviews/${interviewId}`);
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Interview API:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Interview API Error:</h3><pre>' + error.message + '</pre>';
            }
        }

        async function testJobsAPI() {
            try {
                const response = await fetch('http://localhost:3001/api/jobs');
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Jobs API:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Jobs API Error:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
