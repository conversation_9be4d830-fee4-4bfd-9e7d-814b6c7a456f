import { NextRequest, NextResponse } from 'next/server'
import { interviewService } from '@/services/interview'

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        { error: 'Interview ID is required' },
        { status: 400 }
      )
    }

    const interview = await interviewService.getInterview(id)

    if (!interview) {
      return NextResponse.json(
        { error: 'Interview not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(interview)
  } catch (error) {
    console.error('Error getting interview:', error)
    return NextResponse.json(
      { error: 'Failed to get interview' },
      { status: 500 }
    )
  }
}
