import { NextApiRequest, NextApiResponse } from 'next'
import { interviewService } from '@/services/interview'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Interview ID is required' })
  }

  try {
    switch (req.method) {
      case 'GET':
        return await getInterview(req, res, id)
      default:
        res.setHeader('Allow', ['GET'])
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('API Error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

async function getInterview(req: NextApiRequest, res: NextApiResponse, interviewId: string) {
  try {
    const interview = await interviewService.getInterview(interviewId)
    
    if (!interview) {
      return res.status(404).json({ error: 'Interview not found' })
    }

    return res.status(200).json(interview)
  } catch (error) {
    console.error('Error getting interview:', error)
    return res.status(500).json({ error: 'Failed to get interview' })
  }
}
