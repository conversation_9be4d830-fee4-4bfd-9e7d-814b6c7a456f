import { prisma } from '@/lib/prisma'
import { cerebrasService, InterviewContext } from './cerebras'
import { InterviewStatus, MessageSpeaker } from '@prisma/client'

export interface CreateInterviewData {
  candidateId: string
  jobId: string
}

export interface StartInterviewData {
  interviewId: string
}

export interface ProcessMessageData {
  interviewId: string
  message: string
  audioUrl?: string
}

export class InterviewService {
  private interviewContexts: Map<string, InterviewContext> = new Map()

  async createInterview(data: CreateInterviewData) {
    try {
      const interview = await prisma.interview.create({
        data: {
          candidateId: data.candidateId,
          jobId: data.jobId,
          status: InterviewStatus.SCHEDULED
        },
        include: {
          candidate: true,
          job: true
        }
      })

      return interview
    } catch (error) {
      console.error('Error creating interview:', error)
      throw new Error('Failed to create interview')
    }
  }

  async startInterview(data: StartInterviewData) {
    try {
      const interview = await prisma.interview.findUnique({
        where: { id: data.interviewId },
        include: {
          candidate: true,
          job: true
        }
      })

      if (!interview) {
        throw new Error('Interview not found')
      }

      // Update interview status
      await prisma.interview.update({
        where: { id: data.interviewId },
        data: {
          status: InterviewStatus.IN_PROGRESS,
          startedAt: new Date()
        }
      })

      // Initialize interview context
      const context: InterviewContext = {
        jobDescription: interview.job.description,
        candidateResume: interview.candidate.resumeUrl || undefined,
        conversationHistory: [],
        currentPhase: 'introduction',
        evaluationCriteria: [
          'technical_skills',
          'communication',
          'problem_solving',
          'cultural_fit',
          'experience_relevance'
        ]
      }

      this.interviewContexts.set(data.interviewId, context)

      // Generate initial question
      const initialQuestion = cerebrasService.generateInitialQuestion(interview.job.description)

      // Save initial AI message
      await prisma.interviewMessage.create({
        data: {
          interviewId: data.interviewId,
          speaker: MessageSpeaker.AI_INTERVIEWER,
          message: initialQuestion
        }
      })

      return {
        interviewId: data.interviewId,
        message: initialQuestion,
        status: InterviewStatus.IN_PROGRESS
      }
    } catch (error) {
      console.error('Error starting interview:', error)
      throw new Error('Failed to start interview')
    }
  }

  async processMessage(data: ProcessMessageData) {
    try {
      const context = this.interviewContexts.get(data.interviewId)
      if (!context) {
        throw new Error('Interview context not found')
      }

      // Save candidate message
      await prisma.interviewMessage.create({
        data: {
          interviewId: data.interviewId,
          speaker: MessageSpeaker.CANDIDATE,
          message: data.message,
          audioUrl: data.audioUrl
        }
      })

      // Generate AI response
      const aiResponse = await cerebrasService.generateResponse(data.message, context)

      // Update context phase if needed
      if (aiResponse.nextPhase) {
        context.currentPhase = aiResponse.nextPhase
      }

      // Save AI response
      await prisma.interviewMessage.create({
        data: {
          interviewId: data.interviewId,
          speaker: MessageSpeaker.AI_INTERVIEWER,
          message: aiResponse.message
        }
      })

      // Save evaluation if provided
      if (aiResponse.evaluation) {
        await prisma.evaluation.create({
          data: {
            interviewId: data.interviewId,
            criteria: aiResponse.evaluation.criteria,
            score: aiResponse.evaluation.score,
            feedback: aiResponse.evaluation.feedback
          }
        })
      }

      // End interview if needed
      if (aiResponse.shouldEnd) {
        await this.endInterview(data.interviewId)
      }

      return {
        message: aiResponse.message,
        evaluation: aiResponse.evaluation,
        shouldEnd: aiResponse.shouldEnd,
        currentPhase: context.currentPhase
      }
    } catch (error) {
      console.error('Error processing message:', error)
      throw new Error('Failed to process message')
    }
  }

  async endInterview(interviewId: string) {
    try {
      // Calculate final score
      const evaluations = await prisma.evaluation.findMany({
        where: { interviewId }
      })

      const finalScore = evaluations.length > 0
        ? evaluations.reduce((sum, evaluation) => sum + evaluation.score, 0) / evaluations.length
        : 0

      // Update interview
      await prisma.interview.update({
        where: { id: interviewId },
        data: {
          status: InterviewStatus.COMPLETED,
          endedAt: new Date(),
          evaluationScore: finalScore
        }
      })

      // Clean up context
      this.interviewContexts.delete(interviewId)

      return { finalScore }
    } catch (error) {
      console.error('Error ending interview:', error)
      throw new Error('Failed to end interview')
    }
  }

  async getInterview(interviewId: string) {
    try {
      const interview = await prisma.interview.findUnique({
        where: { id: interviewId },
        include: {
          candidate: true,
          job: true,
          messages: {
            orderBy: { timestamp: 'asc' }
          },
          evaluations: {
            orderBy: { createdAt: 'asc' }
          }
        }
      })

      return interview
    } catch (error) {
      console.error('Error getting interview:', error)
      throw new Error('Failed to get interview')
    }
  }

  async getInterviewsByCandidate(candidateId: string) {
    try {
      const interviews = await prisma.interview.findMany({
        where: { candidateId },
        include: {
          job: true,
          evaluations: true
        },
        orderBy: { createdAt: 'desc' }
      })

      return interviews
    } catch (error) {
      console.error('Error getting interviews by candidate:', error)
      throw new Error('Failed to get interviews')
    }
  }

  async getAllInterviews() {
    try {
      const interviews = await prisma.interview.findMany({
        include: {
          candidate: true,
          job: true,
          evaluations: true
        },
        orderBy: { createdAt: 'desc' }
      })

      return interviews
    } catch (error) {
      console.error('Error getting all interviews:', error)
      throw new Error('Failed to get interviews')
    }
  }
}

export const interviewService = new InterviewService()
