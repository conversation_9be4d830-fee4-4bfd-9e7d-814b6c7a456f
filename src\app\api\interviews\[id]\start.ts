import { NextApiRequest, NextApiResponse } from 'next'
import { interviewService } from '@/services/interview'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query

  if (!id || typeof id !== 'string') {
    return res.status(400).json({ error: 'Interview ID is required' })
  }

  try {
    switch (req.method) {
      case 'POST':
        return await startInterview(req, res, id)
      default:
        res.setHeader('Allow', ['POST'])
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('API Error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

async function startInterview(req: NextApiRequest, res: NextApiResponse, interviewId: string) {
  try {
    const result = await interviewService.startInterview({ interviewId })
    return res.status(200).json(result)
  } catch (error) {
    console.error('Error starting interview:', error)
    return res.status(500).json({ error: 'Failed to start interview' })
  }
}
