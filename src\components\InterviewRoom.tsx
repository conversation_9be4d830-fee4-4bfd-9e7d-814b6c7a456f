'use client'

import { useState, useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import { Mic, MicOff, Volume2, VolumeX, Phone, PhoneOff, MessageSquare } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { VoiceService, VoiceMessage } from '@/services/voice'
import VoiceActivityIndicator from './VoiceActivityIndicator'

interface InterviewRoomProps {
  interviewId: string
  candidateId: string
  jobId: string
}

interface Message {
  id: string
  speaker: 'AI_INTERVIEWER' | 'CANDIDATE'
  message: string
  timestamp: number
  audioUrl?: string
}

interface EvaluationUpdate {
  criteria: string
  score: number
  feedback: string
}

export default function InterviewRoom({ interviewId, candidateId, jobId }: InterviewRoomProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [isRecording, setIsRecording] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [interviewStatus, setInterviewStatus] = useState<'waiting' | 'active' | 'ended'>('waiting')
  const [currentPhase, setCurrentPhase] = useState<string>('introduction')
  const [evaluations, setEvaluations] = useState<EvaluationUpdate[]>([])
  const [textInput, setTextInput] = useState('')
  const [useVoice, setUseVoice] = useState(true)
  const [voiceConnected, setVoiceConnected] = useState(false)
  const [isTranscribing, setIsTranscribing] = useState(false)

  const voiceServiceRef = useRef<VoiceService | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Initialize Socket.io connection for fallback
    const newSocket = io(process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3001', {
      path: '/api/socket',
    })

    newSocket.on('connect', () => {
      console.log('Connected to Next.js server')
      setIsConnected(true)

      // Join the interview room
      newSocket.emit('join_interview', {
        interviewId,
        candidateId,
        jobId
      })
    })

    newSocket.on('disconnect', () => {
      console.log('Disconnected from Next.js server')
      setIsConnected(false)
    })

    newSocket.on('ai_response', (data) => {
      if (!useVoice) {
        console.log('Received AI response:', data)
        setMessages(prev => [...prev, {
          id: Date.now().toString(),
          speaker: 'AI_INTERVIEWER',
          message: data.message,
          timestamp: data.timestamp,
          audioUrl: data.audioUrl
        }])
      }
    })

    newSocket.on('evaluation_update', (data) => {
      console.log('Received evaluation update:', data)
      setEvaluations(prev => [...prev, data])
    })

    newSocket.on('interview_complete', (data) => {
      console.log('Interview completed:', data)
      setInterviewStatus('ended')
    })

    newSocket.on('error', (message) => {
      console.error('Socket error:', message)
    })

    setSocket(newSocket)

    return () => {
      newSocket.close()
    }
  }, [interviewId, candidateId, jobId, useVoice])

  // Initialize voice service
  useEffect(() => {
    if (useVoice && interviewStatus === 'active') {
      initializeVoiceService()
    }

    return () => {
      if (voiceServiceRef.current) {
        voiceServiceRef.current.disconnect()
      }
    }
  }, [useVoice, interviewStatus])

  const initializeVoiceService = async () => {
    try {
      const voiceService = new VoiceService(interviewId)
      voiceServiceRef.current = voiceService

      voiceService.onMessage((message: VoiceMessage) => {
        handleVoiceMessage(message)
      })

      voiceService.onError((error: string) => {
        console.error('Voice service error:', error)
        setVoiceConnected(false)
      })

      await voiceService.connect()
      setVoiceConnected(true)
      console.log('Voice service connected')

    } catch (error) {
      console.error('Failed to initialize voice service:', error)
      setVoiceConnected(false)
    }
  }

  const handleVoiceMessage = (message: VoiceMessage) => {
    if (message.type === 'ai_response') {
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        speaker: 'AI_INTERVIEWER',
        message: message.text,
        timestamp: message.timestamp,
        audioUrl: undefined
      }])

      if (message.phase) {
        setCurrentPhase(message.phase)
      }
    } else if (message.type === 'transcription') {
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        speaker: 'CANDIDATE',
        message: message.text,
        timestamp: message.timestamp,
        audioUrl: undefined
      }])
      setIsTranscribing(false)
    }
  }

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const startInterview = async () => {
    if (!socket) return

    try {
      // Start the interview via API
      const response = await fetch(`/api/interviews/${interviewId}/start`, {
        method: 'POST'
      })

      if (response.ok) {
        setInterviewStatus('active')
        socket.emit('interview_ready', { interviewId })
      }
    } catch (error) {
      console.error('Error starting interview:', error)
    }
  }

  const startRecording = async () => {
    if (useVoice && voiceServiceRef.current) {
      try {
        await voiceServiceRef.current.startRecording()
        setIsRecording(true)
        setIsTranscribing(true)
      } catch (error) {
        console.error('Error starting voice recording:', error)
      }
    }
  }

  const stopRecording = () => {
    if (useVoice && voiceServiceRef.current && isRecording) {
      voiceServiceRef.current.stopRecording()
      setIsRecording(false)
    }
  }

  const sendTextMessage = () => {
    if (!textInput.trim()) return

    const message = textInput.trim()
    setTextInput('')

    if (useVoice && voiceServiceRef.current) {
      // Send via voice service
      voiceServiceRef.current.sendTextMessage(message)

      // Add message to local state
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        speaker: 'CANDIDATE',
        message,
        timestamp: Date.now()
      }])
    } else if (socket) {
      // Fallback to socket.io
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        speaker: 'CANDIDATE',
        message,
        timestamp: Date.now()
      }])

      socket.emit('candidate_response', {
        interviewId,
        message
      })
    }
  }

  const endInterview = () => {
    setInterviewStatus('ended')
    if (socket) {
      socket.disconnect()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>AI Interview Session</CardTitle>
              <div className="flex items-center gap-4">
                <Badge variant={isConnected ? 'default' : 'destructive'}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </Badge>
                {useVoice && (
                  <Badge variant={voiceConnected ? 'default' : 'destructive'}>
                    Voice: {voiceConnected ? 'Connected' : 'Disconnected'}
                  </Badge>
                )}
                <Badge variant="outline">
                  Phase: {currentPhase}
                </Badge>
                <Badge variant={interviewStatus === 'active' ? 'default' : 'secondary'}>
                  {interviewStatus}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setUseVoice(!useVoice)}
                >
                  {useVoice ? <Mic className="w-4 h-4 mr-1" /> : <MessageSquare className="w-4 h-4 mr-1" />}
                  {useVoice ? 'Voice' : 'Text'}
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Interview Area */}
          <div className="lg:col-span-2">
            <Card className="h-[600px] flex flex-col">
              <CardHeader>
                <CardTitle>Conversation</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto mb-4 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.speaker === 'CANDIDATE' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-lg ${
                          message.speaker === 'CANDIDATE'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-900'
                        }`}
                      >
                        <p className="text-sm font-medium mb-1">
                          {message.speaker === 'CANDIDATE' ? 'You' : 'AI Interviewer'}
                        </p>
                        <p>{message.message}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input Area */}
                {interviewStatus === 'active' && (
                  <div className="border-t pt-4">
                    {/* Voice Activity Indicator */}
                    {useVoice && voiceConnected && (
                      <div className="flex justify-center mb-4">
                        <VoiceActivityIndicator
                          isRecording={isRecording}
                          audioLevel={isTranscribing ? 50 : 0}
                        />
                      </div>
                    )}

                    {/* Text Input */}
                    <div className="flex gap-2 mb-3">
                      <input
                        type="text"
                        value={textInput}
                        onChange={(e) => setTextInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendTextMessage()}
                        placeholder={useVoice ? "Type or speak your response..." : "Type your response..."}
                        className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <Button onClick={sendTextMessage} disabled={!textInput.trim()}>
                        Send
                      </Button>
                    </div>

                    {/* Audio Controls */}
                    {useVoice && voiceConnected && (
                      <div className="flex justify-center gap-4">
                        <Button
                          variant={isRecording ? 'destructive' : 'default'}
                          size="lg"
                          onClick={isRecording ? stopRecording : startRecording}
                          className="flex items-center gap-2"
                          disabled={!voiceConnected}
                        >
                          {isRecording ? <MicOff /> : <Mic />}
                          {isRecording ? 'Stop Recording' : 'Start Recording'}
                        </Button>

                        <Button
                          variant="outline"
                          size="lg"
                          onClick={() => setIsMuted(!isMuted)}
                          className="flex items-center gap-2"
                        >
                          {isMuted ? <VolumeX /> : <Volume2 />}
                          {isMuted ? 'Unmute' : 'Mute'}
                        </Button>
                      </div>
                    )}

                    {/* Status Messages */}
                    {useVoice && !voiceConnected && (
                      <div className="text-center text-sm text-yellow-600 mt-2">
                        Voice backend not connected. Using text mode.
                      </div>
                    )}

                    {isTranscribing && (
                      <div className="text-center text-sm text-blue-600 mt-2">
                        Transcribing your speech...
                      </div>
                    )}
                  </div>
                )}

                {/* Start Interview Button */}
                {interviewStatus === 'waiting' && (
                  <div className="text-center">
                    <Button onClick={startInterview} size="lg" className="flex items-center gap-2">
                      <Phone />
                      Start Interview
                    </Button>
                  </div>
                )}

                {/* End Interview Button */}
                {interviewStatus === 'active' && (
                  <div className="text-center mt-4">
                    <Button onClick={endInterview} variant="destructive" size="sm" className="flex items-center gap-2">
                      <PhoneOff />
                      End Interview
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Real-time Evaluation */}
            <Card>
              <CardHeader>
                <CardTitle>Live Evaluation</CardTitle>
              </CardHeader>
              <CardContent>
                {evaluations.length > 0 ? (
                  <div className="space-y-3">
                    {evaluations.slice(-3).map((eval, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm font-medium">{eval.criteria}</span>
                          <Badge variant="outline">{eval.score}/10</Badge>
                        </div>
                        <p className="text-xs text-gray-600">{eval.feedback}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Evaluation will appear here during the interview</p>
                )}
              </CardContent>
            </Card>

            {/* Interview Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Messages</span>
                    <span>{messages.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Current Phase</span>
                    <span className="capitalize">{currentPhase}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Evaluations</span>
                    <span>{evaluations.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
