'use client'

import { useState, useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import { Mic, MicOff, Volume2, VolumeX, Phone, PhoneOff } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface InterviewRoomProps {
  interviewId: string
  candidateId: string
  jobId: string
}

interface Message {
  id: string
  speaker: 'AI_INTERVIEWER' | 'CANDIDATE'
  message: string
  timestamp: number
  audioUrl?: string
}

interface EvaluationUpdate {
  criteria: string
  score: number
  feedback: string
}

export default function InterviewRoom({ interviewId, candidateId, jobId }: InterviewRoomProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [isRecording, setIsRecording] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [interviewStatus, setInterviewStatus] = useState<'waiting' | 'active' | 'ended'>('waiting')
  const [currentPhase, setCurrentPhase] = useState<string>('introduction')
  const [evaluations, setEvaluations] = useState<EvaluationUpdate[]>([])
  const [textInput, setTextInput] = useState('')

  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const audioChunksRef = useRef<Blob[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Initialize Socket.io connection
    const newSocket = io(process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3000', {
      path: '/api/socket',
    })

    newSocket.on('connect', () => {
      console.log('Connected to server')
      setIsConnected(true)
      
      // Join the interview room
      newSocket.emit('join_interview', {
        interviewId,
        candidateId,
        jobId
      })
    })

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server')
      setIsConnected(false)
    })

    newSocket.on('ai_response', (data) => {
      console.log('Received AI response:', data)
      setMessages(prev => [...prev, {
        id: Date.now().toString(),
        speaker: 'AI_INTERVIEWER',
        message: data.message,
        timestamp: data.timestamp,
        audioUrl: data.audioUrl
      }])
    })

    newSocket.on('evaluation_update', (data) => {
      console.log('Received evaluation update:', data)
      setEvaluations(prev => [...prev, data])
    })

    newSocket.on('interview_complete', (data) => {
      console.log('Interview completed:', data)
      setInterviewStatus('ended')
    })

    newSocket.on('error', (message) => {
      console.error('Socket error:', message)
    })

    setSocket(newSocket)

    return () => {
      newSocket.close()
    }
  }, [interviewId, candidateId, jobId])

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const startInterview = async () => {
    if (!socket) return

    try {
      // Start the interview via API
      const response = await fetch(`/api/interviews/${interviewId}/start`, {
        method: 'POST'
      })

      if (response.ok) {
        setInterviewStatus('active')
        socket.emit('interview_ready', { interviewId })
      }
    } catch (error) {
      console.error('Error starting interview:', error)
    }
  }

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const mediaRecorder = new MediaRecorder(stream)
      
      mediaRecorderRef.current = mediaRecorder
      audioChunksRef.current = []

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data)
      }

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' })
        // Here you would typically send the audio to STT service
        // For now, we'll simulate with text input
        console.log('Audio recorded:', audioBlob)
      }

      mediaRecorder.start()
      setIsRecording(true)
    } catch (error) {
      console.error('Error starting recording:', error)
    }
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      
      // Stop all tracks
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop())
    }
  }

  const sendTextMessage = () => {
    if (!socket || !textInput.trim()) return

    const message = textInput.trim()
    setTextInput('')

    // Add message to local state
    setMessages(prev => [...prev, {
      id: Date.now().toString(),
      speaker: 'CANDIDATE',
      message,
      timestamp: Date.now()
    }])

    // Send to server
    socket.emit('candidate_response', {
      interviewId,
      message
    })
  }

  const endInterview = () => {
    setInterviewStatus('ended')
    if (socket) {
      socket.disconnect()
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>AI Interview Session</CardTitle>
              <div className="flex items-center gap-4">
                <Badge variant={isConnected ? 'default' : 'destructive'}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </Badge>
                <Badge variant="outline">
                  Phase: {currentPhase}
                </Badge>
                <Badge variant={interviewStatus === 'active' ? 'default' : 'secondary'}>
                  {interviewStatus}
                </Badge>
              </div>
            </div>
          </CardHeader>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Interview Area */}
          <div className="lg:col-span-2">
            <Card className="h-[600px] flex flex-col">
              <CardHeader>
                <CardTitle>Conversation</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto mb-4 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.speaker === 'CANDIDATE' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[80%] p-3 rounded-lg ${
                          message.speaker === 'CANDIDATE'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-900'
                        }`}
                      >
                        <p className="text-sm font-medium mb-1">
                          {message.speaker === 'CANDIDATE' ? 'You' : 'AI Interviewer'}
                        </p>
                        <p>{message.message}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>

                {/* Input Area */}
                {interviewStatus === 'active' && (
                  <div className="border-t pt-4">
                    <div className="flex gap-2 mb-3">
                      <input
                        type="text"
                        value={textInput}
                        onChange={(e) => setTextInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendTextMessage()}
                        placeholder="Type your response..."
                        className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <Button onClick={sendTextMessage} disabled={!textInput.trim()}>
                        Send
                      </Button>
                    </div>
                    
                    {/* Audio Controls */}
                    <div className="flex justify-center gap-4">
                      <Button
                        variant={isRecording ? 'destructive' : 'default'}
                        size="lg"
                        onClick={isRecording ? stopRecording : startRecording}
                        className="flex items-center gap-2"
                      >
                        {isRecording ? <MicOff /> : <Mic />}
                        {isRecording ? 'Stop Recording' : 'Start Recording'}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="lg"
                        onClick={() => setIsMuted(!isMuted)}
                        className="flex items-center gap-2"
                      >
                        {isMuted ? <VolumeX /> : <Volume2 />}
                        {isMuted ? 'Unmute' : 'Mute'}
                      </Button>
                    </div>
                  </div>
                )}

                {/* Start Interview Button */}
                {interviewStatus === 'waiting' && (
                  <div className="text-center">
                    <Button onClick={startInterview} size="lg" className="flex items-center gap-2">
                      <Phone />
                      Start Interview
                    </Button>
                  </div>
                )}

                {/* End Interview Button */}
                {interviewStatus === 'active' && (
                  <div className="text-center mt-4">
                    <Button onClick={endInterview} variant="destructive" size="sm" className="flex items-center gap-2">
                      <PhoneOff />
                      End Interview
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Real-time Evaluation */}
            <Card>
              <CardHeader>
                <CardTitle>Live Evaluation</CardTitle>
              </CardHeader>
              <CardContent>
                {evaluations.length > 0 ? (
                  <div className="space-y-3">
                    {evaluations.slice(-3).map((eval, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm font-medium">{eval.criteria}</span>
                          <Badge variant="outline">{eval.score}/10</Badge>
                        </div>
                        <p className="text-xs text-gray-600">{eval.feedback}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Evaluation will appear here during the interview</p>
                )}
              </CardContent>
            </Card>

            {/* Interview Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Messages</span>
                    <span>{messages.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Current Phase</span>
                    <span className="capitalize">{currentPhase}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Evaluations</span>
                    <span>{evaluations.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
