@echo off
echo 🚀 Setting up AI Interview Voice Backend
echo.

cd voice_backend

echo 📦 Installing Python dependencies...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

echo.
echo ✅ Voice backend setup complete!
echo.
echo 🎯 To start the voice backend:
echo    cd voice_backend
echo    python main.py
echo.
echo 📍 The voice backend will run on: http://localhost:8000
echo.
pause
