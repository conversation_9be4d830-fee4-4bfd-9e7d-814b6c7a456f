import asyncio
import json
import os
import tempfile
import wave
from typing import Dict, Optional
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import numpy as np
import requests
from dotenv import load_dotenv
import logging
import time
import base64
import io

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="AI Interview Voice Backend")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3001", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class VoiceProcessor:
    def __init__(self):
        self.sambanova_key = os.getenv("SAMBANOVA_KEY")
        self.cerebras_key = os.getenv("CEREBRAS_KEY")

        logger.info("Voice processor initialized")
        logger.info(f"SambaNova key: {'✅' if self.sambanova_key else '❌'}")
        logger.info(f"Cerebras key: {'✅' if self.cerebras_key else '❌'}")

    def initialize_tts(self):
        """Initialize TTS - simplified version"""
        try:
            # For now, we'll use a simple TTS approach
            # In production, you can integrate with Silero or other TTS engines
            logger.info("TTS initialized (simplified)")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize TTS: {e}")
            return False
    
    async def transcribe_with_sambanova(self, audio_data: bytes) -> str:
        """Transcribe audio using SambaNova Whisper API"""
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # Prepare request to SambaNova
            url = "https://api.sambanova.ai/v1/audio/transcriptions"
            headers = {
                "Authorization": f"Bearer {self.sambanova_key}",
            }
            
            with open(temp_file_path, "rb") as audio_file:
                files = {
                    "file": ("audio.wav", audio_file, "audio/wav"),
                    "model": (None, "whisper-1"),
                    "language": (None, "en")
                }
                
                response = requests.post(url, headers=headers, files=files, timeout=30)
            
            # Clean up temp file
            os.unlink(temp_file_path)
            
            if response.status_code == 200:
                result = response.json()
                return result.get("text", "").strip()
            else:
                logger.error(f"SambaNova API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Transcription error: {e}")
            return ""
    
    async def synthesize_speech(self, text: str) -> bytes:
        """Convert text to speech - simplified version"""
        try:
            # For now, return empty bytes - the frontend will handle text display
            # In production, integrate with TTS service like:
            # - Silero TTS
            # - ElevenLabs API
            # - OpenAI TTS
            logger.info(f"TTS request for: {text[:50]}...")

            # Simulate TTS processing time
            await asyncio.sleep(0.5)

            return b""  # Return empty for now

        except Exception as e:
            logger.error(f"TTS error: {e}")
            return b""
    
    async def get_ai_response(self, message: str, interview_context: dict) -> str:
        """Get AI response from Cerebras"""
        try:
            url = "https://api.cerebras.ai/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.cerebras_key}",
                "Content-Type": "application/json"
            }
            
            # Build context-aware prompt
            job_title = interview_context.get("job_title", "Software Developer")
            phase = interview_context.get("phase", "introduction")
            
            system_prompt = f"""You are an AI HR interviewer conducting a professional interview for a {job_title} position. 
            Current phase: {phase}. Keep responses concise (2-3 sentences), professional, and engaging. 
            Ask relevant follow-up questions based on the candidate's response."""
            
            payload = {
                "model": "llama3.1-8b",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message}
                ],
                "temperature": 0.7,
                "max_tokens": 150
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"].strip()
            else:
                logger.error(f"Cerebras API error: {response.status_code}")
                return "I apologize, but I'm having technical difficulties. Could you please repeat that?"
                
        except Exception as e:
            logger.error(f"AI response error: {e}")
            return "I'm sorry, I didn't catch that. Could you please try again?"

# Global voice processor instance
voice_processor = VoiceProcessor()

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.interview_contexts: Dict[str, dict] = {}
    
    async def connect(self, websocket: WebSocket, interview_id: str):
        await websocket.accept()
        self.active_connections[interview_id] = websocket
        self.interview_contexts[interview_id] = {
            "phase": "introduction",
            "job_title": "Software Developer",
            "message_count": 0
        }
        logger.info(f"Client connected for interview {interview_id}")
    
    def disconnect(self, interview_id: str):
        if interview_id in self.active_connections:
            del self.active_connections[interview_id]
        if interview_id in self.interview_contexts:
            del self.interview_contexts[interview_id]
        logger.info(f"Client disconnected for interview {interview_id}")
    
    async def send_message(self, interview_id: str, message: dict):
        if interview_id in self.active_connections:
            try:
                await self.active_connections[interview_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message: {e}")

manager = ConnectionManager()

@app.websocket("/ws/{interview_id}")
async def websocket_endpoint(websocket: WebSocket, interview_id: str):
    await manager.connect(websocket, interview_id)
    
    try:
        # Initialize TTS
        voice_processor.initialize_tts()

        # Send welcome message
        welcome_text = f"Hello! Welcome to your AI interview. I'm ready to begin when you are. Please tell me about yourself."

        # Generate welcome audio
        welcome_audio = await voice_processor.synthesize_speech(welcome_text)

        await manager.send_message(interview_id, {
            "type": "ai_response",
            "text": welcome_text,
            "audio": welcome_audio.hex() if welcome_audio else None,
            "timestamp": time.time()
        })
        
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message["type"] == "audio_chunk":
                # Process audio chunk
                audio_data = bytes.fromhex(message["audio_data"])
                
                # Transcribe with SambaNova
                transcription = await voice_processor.transcribe_with_sambanova(audio_data)
                
                if transcription:
                    # Send transcription back to client
                    await manager.send_message(interview_id, {
                        "type": "transcription",
                        "text": transcription,
                        "timestamp": time.time()
                    })
                    
                    # Get AI response
                    context = manager.interview_contexts.get(interview_id, {})
                    ai_response = await voice_processor.get_ai_response(transcription, context)
                    
                    # Generate speech
                    response_audio = await voice_processor.synthesize_speech(ai_response)
                    
                    # Update context
                    context["message_count"] = context.get("message_count", 0) + 1
                    if context["message_count"] > 3 and context["phase"] == "introduction":
                        context["phase"] = "technical"
                    elif context["message_count"] > 8 and context["phase"] == "technical":
                        context["phase"] = "behavioral"
                    
                    # Send AI response
                    await manager.send_message(interview_id, {
                        "type": "ai_response",
                        "text": ai_response,
                        "audio": response_audio.hex() if response_audio else None,
                        "phase": context["phase"],
                        "timestamp": time.time()
                    })
            
            elif message["type"] == "text_message":
                # Handle text input
                text = message["text"]
                context = manager.interview_contexts.get(interview_id, {})
                
                # Get AI response
                ai_response = await voice_processor.get_ai_response(text, context)
                
                # Generate speech
                response_audio = await voice_processor.synthesize_speech(ai_response)
                
                # Send response
                await manager.send_message(interview_id, {
                    "type": "ai_response",
                    "text": ai_response,
                    "audio": response_audio.hex() if response_audio else None,
                    "timestamp": time.time()
                })
    
    except WebSocketDisconnect:
        manager.disconnect(interview_id)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(interview_id)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "message": "Voice backend is running"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
