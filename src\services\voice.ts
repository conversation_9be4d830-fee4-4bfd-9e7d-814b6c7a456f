export interface VoiceMessage {
  type: 'ai_response' | 'transcription' | 'error'
  text: string
  audio?: string
  phase?: string
  timestamp: number
}

export interface AudioChunk {
  type: 'audio_chunk'
  audio_data: string
  timestamp: number
}

export interface TextMessage {
  type: 'text_message'
  text: string
  timestamp: number
}

export class VoiceService {
  private ws: WebSocket | null = null
  private mediaRecorder: MediaRecorder | null = null
  private audioContext: AudioContext | null = null
  private isRecording = false
  private audioChunks: Blob[] = []
  private onMessageCallback: ((message: VoiceMessage) => void) | null = null
  private onErrorCallback: ((error: string) => void) | null = null
  private recordingInterval: NodeJS.Timeout | null = null

  constructor(
    private interviewId: string,
    private voiceBackendUrl: string = 'ws://localhost:8000'
  ) {}

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(`${this.voiceBackendUrl}/ws/${this.interviewId}`)
        
        this.ws.onopen = () => {
          console.log('Connected to voice backend')
          resolve()
        }
        
        this.ws.onmessage = (event) => {
          try {
            const message: VoiceMessage = JSON.parse(event.data)
            this.handleMessage(message)
          } catch (error) {
            console.error('Error parsing message:', error)
          }
        }
        
        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error)
          this.onErrorCallback?.('Connection error')
          reject(error)
        }
        
        this.ws.onclose = () => {
          console.log('Voice backend connection closed')
          this.cleanup()
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  private handleMessage(message: VoiceMessage) {
    console.log('Received voice message:', message.type)
    
    // Play audio if available
    if (message.audio && message.type === 'ai_response') {
      this.playAudio(message.audio)
    }
    
    // Call message callback
    this.onMessageCallback?.(message)
  }

  private async playAudio(audioHex: string) {
    try {
      // Convert hex string to audio buffer
      const audioData = new Uint8Array(audioHex.match(/.{1,2}/g)!.map(byte => parseInt(byte, 16)))
      
      // Create audio context if not exists
      if (!this.audioContext) {
        this.audioContext = new AudioContext()
      }
      
      // Create audio buffer from raw PCM data
      const audioBuffer = this.audioContext.createBuffer(1, audioData.length / 2, 22050)
      const channelData = audioBuffer.getChannelData(0)
      
      // Convert 16-bit PCM to float32
      for (let i = 0; i < channelData.length; i++) {
        const sample = (audioData[i * 2] | (audioData[i * 2 + 1] << 8))
        channelData[i] = sample < 32768 ? sample / 32768 : (sample - 65536) / 32768
      }
      
      // Play audio
      const source = this.audioContext.createBufferSource()
      source.buffer = audioBuffer
      source.connect(this.audioContext.destination)
      source.start()
      
    } catch (error) {
      console.error('Error playing audio:', error)
    }
  }

  async startRecording(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        } 
      })
      
      this.mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })
      
      this.audioChunks = []
      this.isRecording = true
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data)
        }
      }
      
      this.mediaRecorder.onstop = () => {
        this.processAudioChunks()
      }
      
      // Start recording and collect chunks every 2 seconds
      this.mediaRecorder.start()
      this.recordingInterval = setInterval(() => {
        if (this.mediaRecorder && this.isRecording) {
          this.mediaRecorder.stop()
          this.mediaRecorder.start()
        }
      }, 2000)
      
      console.log('Recording started')
    } catch (error) {
      console.error('Error starting recording:', error)
      this.onErrorCallback?.('Failed to start recording')
    }
  }

  stopRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.isRecording = false
      this.mediaRecorder.stop()
      
      if (this.recordingInterval) {
        clearInterval(this.recordingInterval)
        this.recordingInterval = null
      }
      
      // Stop all tracks
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop())
      
      console.log('Recording stopped')
    }
  }

  private async processAudioChunks(): Promise<void> {
    if (this.audioChunks.length === 0) return
    
    try {
      // Combine audio chunks
      const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' })
      
      // Convert to WAV format
      const arrayBuffer = await audioBlob.arrayBuffer()
      const audioData = await this.convertToWav(arrayBuffer)
      
      // Convert to hex string
      const audioHex = Array.from(new Uint8Array(audioData))
        .map(b => b.toString(16).padStart(2, '0'))
        .join('')
      
      // Send to voice backend
      this.sendAudioChunk(audioHex)
      
      // Clear chunks for next recording
      this.audioChunks = []
      
    } catch (error) {
      console.error('Error processing audio:', error)
    }
  }

  private async convertToWav(audioBuffer: ArrayBuffer): Promise<ArrayBuffer> {
    // Simple WAV conversion - in production, use a proper audio library
    // For now, return the original buffer (the backend will handle conversion)
    return audioBuffer
  }

  private sendAudioChunk(audioHex: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message: AudioChunk = {
        type: 'audio_chunk',
        audio_data: audioHex,
        timestamp: Date.now()
      }
      
      this.ws.send(JSON.stringify(message))
    }
  }

  sendTextMessage(text: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message: TextMessage = {
        type: 'text_message',
        text,
        timestamp: Date.now()
      }
      
      this.ws.send(JSON.stringify(message))
    }
  }

  onMessage(callback: (message: VoiceMessage) => void): void {
    this.onMessageCallback = callback
  }

  onError(callback: (error: string) => void): void {
    this.onErrorCallback = callback
  }

  disconnect(): void {
    this.cleanup()
  }

  private cleanup(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.stopRecording()
    }
    
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval)
      this.recordingInterval = null
    }
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  getRecordingState(): boolean {
    return this.isRecording
  }
}
