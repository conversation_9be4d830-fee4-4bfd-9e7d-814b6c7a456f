// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Candidate {
  id        String   @id @default(cuid())
  name      String
  email     String   @unique
  resumeUrl String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  interviews Interview[]

  @@map("candidates")
}

model JobDescription {
  id           String   @id @default(cuid())
  title        String
  description  String
  requirements String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  interviews Interview[]

  @@map("job_descriptions")
}

model Interview {
  id              String          @id @default(cuid())
  candidateId     String
  jobId           String
  status          InterviewStatus @default(SCHEDULED)
  startedAt       DateTime?
  endedAt         DateTime?
  evaluationScore Float?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  candidate Candidate @relation(fields: [candidateId], references: [id], onDelete: Cascade)
  job       JobDescription @relation(fields: [jobId], references: [id], onDelete: Cascade)

  messages    InterviewMessage[]
  evaluations Evaluation[]

  @@map("interviews")
}

model InterviewMessage {
  id          String      @id @default(cuid())
  interviewId String
  speaker     MessageSpeaker
  message     String
  audioUrl    String?
  timestamp   DateTime    @default(now())

  interview Interview @relation(fields: [interviewId], references: [id], onDelete: Cascade)

  @@map("interview_messages")
}

model Evaluation {
  id          String   @id @default(cuid())
  interviewId String
  criteria    String
  score       Float
  feedback    String
  createdAt   DateTime @default(now())

  interview Interview @relation(fields: [interviewId], references: [id], onDelete: Cascade)

  @@map("evaluations")
}

enum InterviewStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum MessageSpeaker {
  AI_INTERVIEWER
  CANDIDATE
}
