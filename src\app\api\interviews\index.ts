import { NextApiRequest, NextApiResponse } from 'next'
import { interviewService } from '@/services/interview'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'POST':
        return await createInterview(req, res)
      case 'GET':
        return await getInterviews(req, res)
      default:
        res.setHeader('Allow', ['POST', 'GET'])
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('API Error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

async function createInterview(req: NextApiRequest, res: NextApiResponse) {
  const { candidateId, jobId } = req.body

  if (!candidateId || !jobId) {
    return res.status(400).json({ error: 'candidateId and jobId are required' })
  }

  try {
    const interview = await interviewService.createInterview({
      candidateId,
      jobId
    })

    return res.status(201).json(interview)
  } catch (error) {
    console.error('Error creating interview:', error)
    return res.status(500).json({ error: 'Failed to create interview' })
  }
}

async function getInterviews(req: NextApiRequest, res: NextApiResponse) {
  const { candidateId } = req.query

  try {
    let interviews
    if (candidateId) {
      interviews = await interviewService.getInterviewsByCandidate(candidateId as string)
    } else {
      interviews = await interviewService.getAllInterviews()
    }

    return res.status(200).json(interviews)
  } catch (error) {
    console.error('Error getting interviews:', error)
    return res.status(500).json({ error: 'Failed to get interviews' })
  }
}
