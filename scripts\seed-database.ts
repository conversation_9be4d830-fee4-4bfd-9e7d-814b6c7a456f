import { prisma } from '../src/lib/prisma'

async function seedDatabase() {
  try {
    console.log('🌱 Seeding database...')

    // Create sample job descriptions
    const job1 = await prisma.jobDescription.create({
      data: {
        title: 'Senior Frontend Developer',
        description: 'We are looking for an experienced Frontend Developer to join our team. You will be responsible for building user-facing web applications using modern JavaScript frameworks.',
        requirements: 'React, TypeScript, Next.js, 3+ years experience, Strong CSS skills, Experience with REST APIs'
      }
    })

    const job2 = await prisma.jobDescription.create({
      data: {
        title: 'Full Stack Engineer',
        description: 'Join our engineering team as a Full Stack Engineer. You will work on both frontend and backend systems, building scalable web applications.',
        requirements: 'Node.js, React, PostgreSQL, Docker, 2+ years experience, API design, Database optimization'
      }
    })

    const job3 = await prisma.jobDescription.create({
      data: {
        title: 'AI/ML Engineer',
        description: 'We are seeking an AI/ML Engineer to develop and deploy machine learning models. You will work on cutting-edge AI projects and research.',
        requirements: 'Python, TensorFlow/PyTorch, Machine Learning, Statistics, 3+ years experience, Research background'
      }
    })

    console.log('✅ Created sample job descriptions:')
    console.log(`   - ${job1.title} (ID: ${job1.id})`)
    console.log(`   - ${job2.title} (ID: ${job2.id})`)
    console.log(`   - ${job3.title} (ID: ${job3.id})`)

    // Create a sample candidate
    const candidate = await prisma.candidate.create({
      data: {
        name: 'John Doe',
        email: '<EMAIL>',
        resumeUrl: 'https://example.com/resume.pdf'
      }
    })

    console.log('✅ Created sample candidate:')
    console.log(`   - ${candidate.name} (ID: ${candidate.id})`)

    // Create a sample interview
    const interview = await prisma.interview.create({
      data: {
        candidateId: candidate.id,
        jobId: job1.id,
        status: 'SCHEDULED'
      }
    })

    console.log('✅ Created sample interview:')
    console.log(`   - Interview ID: ${interview.id}`)
    console.log(`   - Candidate: ${candidate.name}`)
    console.log(`   - Job: ${job1.title}`)

    console.log('\n🎉 Database seeded successfully!')
    console.log('\n📍 You can now:')
    console.log('   1. Go to http://localhost:3001/candidate')
    console.log('   2. Create a new candidate profile')
    console.log('   3. Start an interview with any of the sample jobs')
    console.log(`   4. Or test with the existing interview: http://localhost:3001/interview/${interview.id}`)

  } catch (error) {
    console.error('❌ Error seeding database:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedDatabase()
