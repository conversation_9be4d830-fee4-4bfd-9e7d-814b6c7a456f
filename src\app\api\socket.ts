import { NextApiRequest } from 'next'
import { Server as ServerIO } from 'socket.io'
import { Server as NetServer } from 'http'
import {
  NextApiResponseServerIO,
  ServerToClientEvents,
  ClientToServerEvents,
  InterServerEvents,
  SocketData
} from '@/lib/socket'
import { interviewService } from '@/services/interview'

export default function SocketHandler(req: NextApiRequest, res: NextApiResponseServerIO) {
  if (res.socket.server.io) {
    console.log('Socket is already running')
  } else {
    console.log('Socket is initializing')
    const io = new ServerIO<
      ClientToServerEvents,
      ServerToClientEvents,
      InterServerEvents,
      SocketData
    >(res.socket.server as any, {
      path: '/api/socket',
      addTrailingSlash: false,
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    })

    res.socket.server.io = io

    io.on('connection', (socket) => {
      console.log('Client connected:', socket.id)

      socket.on('join_interview', (data) => {
        console.log('Client joining interview:', data.interviewId)
        socket.data.interviewId = data.interviewId
        socket.data.candidateId = data.candidateId
        socket.data.role = 'candidate'
        socket.join(`interview-${data.interviewId}`)

        // Notify that candidate is ready
        socket.to(`interview-${data.interviewId}`).emit('ai_response', {
          interviewId: data.interviewId,
          message: 'Candidate has joined the interview. Starting in a moment...',
          timestamp: Date.now()
        })
      })

      socket.on('audio_chunk', async (data) => {
        console.log('Received audio chunk for interview:', data.interviewId)
        // Here we'll process the audio chunk with STT
        // For now, just acknowledge receipt
        socket.emit('ai_response', {
          interviewId: data.interviewId,
          message: 'Audio received and processing...',
          timestamp: Date.now()
        })
      })

      socket.on('interview_ready', async (data) => {
        console.log('Interview ready:', data.interviewId)
        try {
          // Start the interview using our service
          const result = await interviewService.startInterview({ interviewId: data.interviewId })

          socket.emit('ai_response', {
            interviewId: data.interviewId,
            message: result.message,
            timestamp: Date.now()
          })
        } catch (error) {
          console.error('Error starting interview:', error)
          socket.emit('error', 'Failed to start interview')
        }
      })

      socket.on('candidate_response', async (data) => {
        console.log('Candidate response:', data.message)
        try {
          // Process the candidate's response using our AI service
          const result = await interviewService.processMessage({
            interviewId: data.interviewId,
            message: data.message
          })

          // Send AI response
          socket.emit('ai_response', {
            interviewId: data.interviewId,
            message: result.message,
            timestamp: Date.now()
          })

          // Send evaluation update if available
          if (result.evaluation) {
            socket.emit('evaluation_update', {
              interviewId: data.interviewId,
              criteria: result.evaluation.criteria,
              score: result.evaluation.score,
              feedback: result.evaluation.feedback
            })
          }

          // End interview if needed
          if (result.shouldEnd) {
            const finalResult = await interviewService.endInterview(data.interviewId)
            socket.emit('interview_complete', {
              interviewId: data.interviewId,
              finalScore: finalResult.finalScore
            })
          }
        } catch (error) {
          console.error('Error processing candidate response:', error)
          socket.emit('error', 'Failed to process your response. Please try again.')
        }
      })

      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id)
      })
    })
  }
  res.end()
}

export const config = {
  api: {
    bodyParser: false,
  },
}
