import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Get counts of all entities
    const candidatesCount = await prisma.candidate.count()
    const jobsCount = await prisma.jobDescription.count()
    const interviewsCount = await prisma.interview.count()
    const messagesCount = await prisma.interviewMessage.count()
    const evaluationsCount = await prisma.evaluation.count()

    // Get recent data
    const recentCandidates = await prisma.candidate.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' }
    })

    const recentJobs = await prisma.jobDescription.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' }
    })

    const recentInterviews = await prisma.interview.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        candidate: true,
        job: true
      }
    })

    return NextResponse.json({
      status: 'healthy',
      database: {
        connected: true,
        counts: {
          candidates: candidatesCount,
          jobs: jobsCount,
          interviews: interviewsCount,
          messages: messagesCount,
          evaluations: evaluationsCount
        }
      },
      recentData: {
        candidates: recentCandidates,
        jobs: recentJobs,
        interviews: recentInterviews
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        hasDatabase: !!process.env.DATABASE_URL,
        hasCerebras: !!process.env.CEREBRAS_KEY,
        hasSambanova: !!process.env.SAMBANOVA_KEY
      }
    })
  } catch (error) {
    console.error('Debug API error:', error)
    return NextResponse.json(
      { 
        status: 'error', 
        error: error instanceof Error ? error.message : 'Unknown error',
        database: { connected: false }
      },
      { status: 500 }
    )
  }
}
