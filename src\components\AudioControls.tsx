'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Mic, MicOff, Volume2, VolumeX, Settings } from 'lucide-react'
import VoiceActivityIndicator from './VoiceActivityIndicator'

interface AudioControlsProps {
  isRecording: boolean
  isMuted: boolean
  onStartRecording: () => void
  onStopRecording: () => void
  onToggleMute: () => void
  onAudioTest?: () => void
}

export default function AudioControls({
  isRecording,
  isMuted,
  onStartRecording,
  onStopRecording,
  onToggleMute,
  onAudioTest
}: AudioControlsProps) {
  const [audioLevel, setAudioLevel] = useState(0)
  const [showSettings, setShowSettings] = useState(false)
  const [selectedMicrophone, setSelectedMicrophone] = useState<string>('')
  const [selectedSpeaker, setSelectedSpeaker] = useState<string>('')
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([])

  useEffect(() => {
    // Get available audio devices
    navigator.mediaDevices.enumerateDevices()
      .then(deviceList => {
        const audioDevices = deviceList.filter(device => 
          device.kind === 'audioinput' || device.kind === 'audiooutput'
        )
        setDevices(audioDevices)
      })
      .catch(console.error)
  }, [])

  const testMicrophone = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const audioContext = new AudioContext()
      const analyser = audioContext.createAnalyser()
      const microphone = audioContext.createMediaStreamSource(stream)
      
      microphone.connect(analyser)
      analyser.fftSize = 256
      
      const bufferLength = analyser.frequencyBinCount
      const dataArray = new Uint8Array(bufferLength)
      
      const checkLevel = () => {
        analyser.getByteFrequencyData(dataArray)
        const average = dataArray.reduce((a, b) => a + b) / bufferLength
        setAudioLevel(average)
      }
      
      const interval = setInterval(checkLevel, 100)
      
      // Stop after 3 seconds
      setTimeout(() => {
        clearInterval(interval)
        stream.getTracks().forEach(track => track.stop())
        audioContext.close()
        setAudioLevel(0)
      }, 3000)
      
    } catch (error) {
      console.error('Error testing microphone:', error)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Voice Activity Indicator */}
          <div className="flex justify-center">
            <VoiceActivityIndicator 
              isRecording={isRecording} 
              audioLevel={audioLevel}
            />
          </div>

          {/* Main Controls */}
          <div className="flex justify-center space-x-4">
            <Button
              variant={isRecording ? 'destructive' : 'default'}
              size="lg"
              onClick={isRecording ? onStopRecording : onStartRecording}
              className="flex items-center space-x-2"
            >
              {isRecording ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              <span>{isRecording ? 'Stop' : 'Record'}</span>
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={onToggleMute}
              className="flex items-center space-x-2"
            >
              {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
              <span>{isMuted ? 'Unmute' : 'Mute'}</span>
            </Button>

            <Button
              variant="outline"
              size="lg"
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center space-x-2"
            >
              <Settings className="w-5 h-5" />
            </Button>
          </div>

          {/* Audio Settings */}
          {showSettings && (
            <div className="space-y-4 border-t pt-4">
              <h4 className="font-medium text-sm">Audio Settings</h4>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Microphone
                  </label>
                  <select 
                    value={selectedMicrophone}
                    onChange={(e) => setSelectedMicrophone(e.target.value)}
                    className="w-full text-xs border rounded px-2 py-1"
                  >
                    <option value="">Default</option>
                    {devices
                      .filter(device => device.kind === 'audioinput')
                      .map(device => (
                        <option key={device.deviceId} value={device.deviceId}>
                          {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                        </option>
                      ))
                    }
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Speaker
                  </label>
                  <select 
                    value={selectedSpeaker}
                    onChange={(e) => setSelectedSpeaker(e.target.value)}
                    className="w-full text-xs border rounded px-2 py-1"
                  >
                    <option value="">Default</option>
                    {devices
                      .filter(device => device.kind === 'audiooutput')
                      .map(device => (
                        <option key={device.deviceId} value={device.deviceId}>
                          {device.label || `Speaker ${device.deviceId.slice(0, 8)}`}
                        </option>
                      ))
                    }
                  </select>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={testMicrophone}
                  className="w-full text-xs"
                >
                  Test Microphone
                </Button>
              </div>
            </div>
          )}

          {/* Audio Level Display */}
          {audioLevel > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-600">
                <span>Audio Level</span>
                <span>{Math.round(audioLevel)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-100"
                  style={{ width: `${Math.min(100, audioLevel)}%` }}
                />
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
