@echo off
echo 🚀 Starting AI HR Interview Platform with Voice Support
echo.

echo 📦 Setting up Python Voice Backend...
cd voice_backend

echo Installing Python dependencies...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
python -m pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

echo.
echo 🎯 Starting Voice Backend Server...
start "Voice Backend" python main.py

echo.
echo ⏳ Waiting for voice backend to start...
timeout /t 5 /nobreak > nul

cd ..

echo.
echo 🌐 Starting Next.js Frontend...
start "Frontend" npm run dev

echo.
echo ✅ Both servers are starting!
echo.
echo 📍 Access the application at:
echo    Frontend: http://localhost:3001
echo    Voice Backend: http://localhost:8000
echo.
echo 🎤 Voice Features:
echo    - Real-time speech-to-text with SambaNova Whisper
echo    - Text-to-speech with <PERSON><PERSON><PERSON> TTS
echo    - AI conversation with Cerebras LLM
echo.
echo Press any key to exit...
pause > nul
