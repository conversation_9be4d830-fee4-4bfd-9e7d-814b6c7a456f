import { Server as NetServer } from 'http'
import { NextApiRequest, NextApiResponse } from 'next'
import { Server as ServerIO } from 'socket.io'

export type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: ServerIO
    }
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
}

export interface InterviewSocketData {
  interviewId: string
  candidateId: string
  jobId: string
}

export interface AudioChunkData {
  interviewId: string
  audioData: ArrayBuffer
  timestamp: number
}

export interface AIResponseData {
  interviewId: string
  message: string
  audioUrl?: string
  timestamp: number
}

export interface EvaluationUpdateData {
  interviewId: string
  criteria: string
  score: number
  feedback: string
}

// Socket.io event types
export interface ServerToClientEvents {
  ai_response: (data: AIResponseData) => void
  audio_response: (audioData: ArrayBuffer) => void
  evaluation_update: (data: EvaluationUpdateData) => void
  interview_complete: (data: { interviewId: string; finalScore: number }) => void
  error: (message: string) => void
}

export interface ClientToServerEvents {
  join_interview: (data: InterviewSocketData) => void
  audio_chunk: (data: AudioChunkData) => void
  interview_ready: (data: { interviewId: string }) => void
  candidate_response: (data: { interviewId: string; message: string }) => void
}

export interface InterServerEvents {
  ping: () => void
}

export interface SocketData {
  interviewId?: string
  candidateId?: string
  role?: 'candidate' | 'recruiter'
}
