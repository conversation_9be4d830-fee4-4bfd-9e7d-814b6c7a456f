'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'

interface JobDescription {
  id: string
  title: string
  description: string
  requirements: string
  createdAt: string
}

interface Candidate {
  id: string
  name: string
  email: string
  resumeUrl?: string
}

export default function CandidatePage() {
  const router = useRouter()
  const [jobs, setJobs] = useState<JobDescription[]>([])
  const [candidate, setCandidate] = useState<Candidate | null>(null)
  const [loading, setLoading] = useState(true)
  const [candidateForm, setCandidateForm] = useState({
    name: '',
    email: '',
    resumeUrl: ''
  })

  useEffect(() => {
    fetchJobs()
    // Check if candidate exists in localStorage
    const savedCandidate = localStorage.getItem('candidate')
    if (savedCandidate) {
      setCandidate(JSON.parse(savedCandidate))
    }
  }, [])

  const fetchJobs = async () => {
    try {
      const response = await fetch('/api/jobs')
      if (response.ok) {
        const data = await response.json()
        setJobs(data)
      }
    } catch (error) {
      console.error('Error fetching jobs:', error)
    } finally {
      setLoading(false)
    }
  }

  const createCandidate = async () => {
    try {
      const response = await fetch('/api/candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(candidateForm)
      })

      if (response.ok) {
        const newCandidate = await response.json()
        setCandidate(newCandidate)
        localStorage.setItem('candidate', JSON.stringify(newCandidate))
        setCandidateForm({ name: '', email: '', resumeUrl: '' })
      }
    } catch (error) {
      console.error('Error creating candidate:', error)
    }
  }

  const startInterview = async (jobId: string) => {
    if (!candidate) return

    try {
      // Create interview
      const response = await fetch('/api/interviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          candidateId: candidate.id,
          jobId
        })
      })

      if (response.ok) {
        const interview = await response.json()
        router.push(`/interview/${interview.id}?candidateId=${candidate.id}&jobId=${jobId}`)
      }
    } catch (error) {
      console.error('Error starting interview:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Candidate Portal</h1>
          <p className="text-gray-600">Find and apply for positions with AI-powered interviews</p>
        </div>

        {/* Candidate Registration/Info */}
        {!candidate ? (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Create Your Profile</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <input
                  type="text"
                  placeholder="Full Name"
                  value={candidateForm.name}
                  onChange={(e) => setCandidateForm(prev => ({ ...prev, name: e.target.value }))}
                  className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="email"
                  placeholder="Email Address"
                  value={candidateForm.email}
                  onChange={(e) => setCandidateForm(prev => ({ ...prev, email: e.target.value }))}
                  className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="url"
                  placeholder="Resume URL (optional)"
                  value={candidateForm.resumeUrl}
                  onChange={(e) => setCandidateForm(prev => ({ ...prev, resumeUrl: e.target.value }))}
                  className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <Button 
                onClick={createCandidate}
                disabled={!candidateForm.name || !candidateForm.email}
              >
                Create Profile
              </Button>
            </CardContent>
          </Card>
        ) : (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Welcome back, {candidate.name}!</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600">Email: {candidate.email}</p>
                  {candidate.resumeUrl && (
                    <p className="text-gray-600">
                      Resume: <a href={candidate.resumeUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">View</a>
                    </p>
                  )}
                </div>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setCandidate(null)
                    localStorage.removeItem('candidate')
                  }}
                >
                  Switch Profile
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Available Jobs */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {jobs.map((job) => (
            <Card key={job.id} className="h-full flex flex-col">
              <CardHeader>
                <CardTitle className="text-lg">{job.title}</CardTitle>
                <Badge variant="outline">
                  {new Date(job.createdAt).toLocaleDateString()}
                </Badge>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                <div className="flex-1">
                  <p className="text-gray-600 mb-4 line-clamp-3">{job.description}</p>
                  <div className="mb-4">
                    <h4 className="font-medium mb-2">Requirements:</h4>
                    <p className="text-sm text-gray-600 line-clamp-3">{job.requirements}</p>
                  </div>
                </div>
                <div className="mt-auto">
                  {candidate ? (
                    <Button 
                      onClick={() => startInterview(job.id)}
                      className="w-full"
                    >
                      Start AI Interview
                    </Button>
                  ) : (
                    <Button disabled className="w-full">
                      Create Profile First
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {jobs.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No job positions available at the moment.</p>
              <Link href="/recruiter">
                <Button variant="outline">Are you a recruiter?</Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link href="/">
            <Button variant="outline">Back to Home</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
