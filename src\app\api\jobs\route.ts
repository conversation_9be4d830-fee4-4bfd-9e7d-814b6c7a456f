import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { title, description, requirements } = await request.json()

    if (!title || !description || !requirements) {
      return NextResponse.json(
        { error: 'Title, description, and requirements are required' },
        { status: 400 }
      )
    }

    const job = await prisma.jobDescription.create({
      data: {
        title,
        description,
        requirements
      }
    })

    return NextResponse.json(job, { status: 201 })
  } catch (error) {
    console.error('Error creating job:', error)
    return NextResponse.json(
      { error: 'Failed to create job' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const jobs = await prisma.jobDescription.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        interviews: {
          include: {
            candidate: true
          }
        }
      }
    })

    return NextResponse.json(jobs)
  } catch (error) {
    console.error('Error getting jobs:', error)
    return NextResponse.json(
      { error: 'Failed to get jobs' },
      { status: 500 }
    )
  }
}
