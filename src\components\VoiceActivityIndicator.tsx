'use client'

import { useEffect, useState } from 'react'
import { Mi<PERSON>, MicOff } from 'lucide-react'

interface VoiceActivityIndicatorProps {
  isRecording: boolean
  audioLevel?: number
}

export default function VoiceActivityIndicator({ isRecording, audioLevel = 0 }: VoiceActivityIndicatorProps) {
  const [pulseIntensity, setPulseIntensity] = useState(0)

  useEffect(() => {
    if (isRecording) {
      // Simulate voice activity detection
      const interval = setInterval(() => {
        setPulseIntensity(Math.random() * 100)
      }, 100)
      
      return () => clearInterval(interval)
    } else {
      setPulseIntensity(0)
    }
  }, [isRecording])

  const getIndicatorColor = () => {
    if (!isRecording) return 'bg-gray-400'
    if (pulseIntensity > 70) return 'bg-green-500'
    if (pulseIntensity > 40) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const getIndicatorSize = () => {
    if (!isRecording) return 'w-12 h-12'
    const scale = 1 + (pulseIntensity / 200)
    return `w-12 h-12 scale-${Math.min(150, Math.max(100, Math.round(scale * 100)))}`
  }

  return (
    <div className="flex flex-col items-center space-y-2">
      <div className="relative">
        <div
          className={`
            ${getIndicatorColor()} 
            ${getIndicatorSize()}
            rounded-full 
            flex items-center justify-center 
            transition-all duration-100 ease-out
            ${isRecording ? 'animate-pulse' : ''}
          `}
        >
          {isRecording ? (
            <Mic className="w-6 h-6 text-white" />
          ) : (
            <MicOff className="w-6 h-6 text-white" />
          )}
        </div>
        
        {/* Voice level bars */}
        {isRecording && (
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className={`
                  w-1 bg-blue-500 rounded-full transition-all duration-100
                  ${pulseIntensity > (i + 1) * 20 ? 'h-4' : 'h-1'}
                `}
              />
            ))}
          </div>
        )}
      </div>
      
      <p className="text-xs text-gray-600 text-center">
        {isRecording ? 'Listening...' : 'Click to speak'}
      </p>
    </div>
  )
}
